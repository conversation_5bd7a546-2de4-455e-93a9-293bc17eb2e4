using System.Text.Json;
using ConsoleApp1;
using LinqKit;
using Masar.Core.Dtos.ImprovementOpportunity;
using Masar.Core.Dtos.PlanDtos.Plans;
using Masar.Core.Dtos.PlanDtos.PlanSubtasks;
using Masar.Core.Dtos.PlanDtos.PlanTasks;
using Masar.Core.Dtos.Standard;
using Masar.Core.Dtos.StrategicGoal;
using Masar.Core.Dtos.Tournament;
using Masar.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using OpenAI;
using OpenAI.Chat;


var services = new ServiceCollection();
const string connectionString =
    "Data Source=.,1433;Initial Catalog=masar_ta;Integrated Security=False;User ID=SA;Password=password_135790;Connect Timeout=30;Encrypt=False;TrustServerCertificate=True;ApplicationIntent=ReadWrite;MultiSubnetFailover=False";
services.AddDbContext<NewDataContext>(options => { options.UseSqlServer(connectionString); });
services.AddScoped<NewDataContext>();

var serviceProvider = services.BuildServiceProvider();

using var scope = serviceProvider.CreateScope();
var db = scope.ServiceProvider.GetRequiredService<NewDataContext>();


const string apiKey = "AIzaSyCwz2mgbGGa7DEQRp4xFv-9Ki7vsIjcINM";
const string model = "gemini-2.5-pro-preview-05-06";
// const string model = "gemini-2.5-flash-preview-05-20";

var client = new OpenAIClient(new(apiKey), new()
{
    Endpoint = new Uri("https://generativelanguage.googleapis.com/v1beta"),
});

var chatClient = client.GetChatClient(model);

// AnalyzeKpi(Guid.Parse("76ae66a9-7e04-4ab0-6afc-08d98ecb2dac"));

Console.WriteLine(AnalyzeOpportunity(Guid.Parse("131b0a76-c644-4ac2-c8db-08db08ce9e6d")));

string AnalyzeOpportunity(Guid opportunityId)
{
    var opportunity = db.ImprovementOpportunities
        .AsExpandable()
        .Where(x => x.Id == opportunityId)
        .Select(ImprovementOpportunityGetDto.Mapper("ar", Guid.Empty))
        .SingleOrDefault();

    var tasks = db.PlanTasks
        .AsExpandable()
        .Where(x => x.PlanId == opportunityId)
        .Select(PlanTaskGetDto.Mapper("ar"))
        .ToList();

    var subtasks = db.PlanSubtasks
        .AsExpandable()
        .Where(x => x.Task.PlanId == opportunityId)
        .Select(PlanSubtaskGetDto.Mapper("ar", Guid.Empty, db.Departments))
        .ToList();

    var goals = db.StrategicGoals
        .AsExpandable()
        .Select(StrategicGoalGetDto.Mapper("ar"));

    ChatCompletion completion = chatClient.CompleteChat(
        new SystemChatMessage(Prompts.ImprovementOpportunity.GetSystemPrompt()),
        new UserChatMessage(Prompts.ImprovementOpportunity.GetUserPrompt(opportunity))
    );

    return completion.Content[0].Text;
}

string AnalyzePlan(Guid planId)
{
    var plan = db.Plans
        .AsExpandable()
        .Where(x => x.Id == planId)
        .Select(PlanGetDto.Mapper("ar", Guid.Empty))
        .SingleOrDefault();

    var tasks = db.PlanTasks
        .AsExpandable()
        .Where(x => x.PlanId == planId)
        .Select(PlanTaskGetDto.Mapper("ar"))
        .ToList();

    var subtasks = db.PlanSubtasks
        .AsExpandable()
        .Where(x => x.Task.PlanId == planId)
        .Select(PlanSubtaskGetDto.Mapper("ar", Guid.Empty, db.Departments))
        .ToList();

    var goals = db.StrategicGoals
        .AsExpandable()
        .Select(StrategicGoalGetDto.Mapper("ar"));

    ChatCompletion completion = chatClient.CompleteChat(
        new SystemChatMessage(Prompts.Plan.GetSystemPrompt(goals)),
        new UserChatMessage(Prompts.Plan.GetUserPrompt(plan, tasks, subtasks))
    );

    return completion.Content[0].Text;
}


string AnalyzeTournament(Guid tournamentId)
{
    var tournament = db.Tournaments
        .AsExpandable()
        .Where(x => x.Id == tournamentId)
        .Select(TournamentGetDto.Mapper("ar"))
        .SingleOrDefault();

    var standards = db.Standards
        .AsExpandable()
        .Where(x => x.Pillar.TournamentId == tournamentId)
        .Select(StandardGetDto.Mapper("ar", Guid.Empty, true))
        .ToList();

    var goals = db.StrategicGoals
        .AsExpandable()
        .Select(StrategicGoalGetDto.Mapper("ar"));

    ChatCompletion completion = chatClient.CompleteChat(
        new SystemChatMessage(Prompts.Tournament.GetSystemPrompt(goals)),
        new UserChatMessage(Prompts.Tournament.GetUserPrompt(tournament, standards))
    );

    return completion.Content[0].Text;
}

string AnalyzeKpi(Guid kpiId)
{
    var kpi = db.Kpis
        .AsExpandable()
        .Where(x => x.Id == kpiId)
        .Map(new NewKpiGetDtoMappingStrategy(true, includeAllResults: true))
        .Single();

    ChatCompletion completion = chatClient.CompleteChat(
        new SystemChatMessage(Prompts.Kpi.GetSystemPrompt()),
        new UserChatMessage(Prompts.Kpi.GetUserPrompt(kpi))
    );

    return completion.Content[0].Text;
}