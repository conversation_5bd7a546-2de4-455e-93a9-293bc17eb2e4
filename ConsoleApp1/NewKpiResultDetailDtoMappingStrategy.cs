using System.Linq.Expressions;
using LinqKit;
using Masar.Core;
using Masar.Core.Abstraction;
using Masar.Core.Constants;
using Masar.Core.Dtos.Department;
using Masar.Core.Dtos.Kpi;
using Masar.Core.Dtos.KpiResult;
using Masar.Core.Dtos.KpiResultTargetSettingMethod;
using Masar.Core.Models.DomainClasses.App;
using Masar.Core.Models.SqlFunctions;

namespace ConsoleApp1;

public class NewKpiResultDetailDtoMappingStrategy : IDtoMappingStrategy<KpiResult, KpiResultDetailDto>
{
    private readonly bool _canAchievedBeNegative;

    public NewKpiResultDetailDtoMappingStrategy(
        bool canAchievedBeNegative)
    {
        _canAchievedBeNegative = canAchievedBeNegative;
    }

    public IEnumerable<KpiResultDetailDto> Execute(IQueryable<KpiResult> source)
    {
        var results = source.Select(Mapper(HelperFunctions.GetLanguageCode(), _canAchievedBeNegative)).ToList();

        // Bring all the periods associated with
        // the mapped results.
        var resultIds = results.Select(x => x.Id).ToArray();


        var periodExpression = PeriodMapper(_canAchievedBeNegative);

        var groupedResults = source
            .Where(x => resultIds.Contains(x.Id))
            .SelectMany(x => x.Periods)
            .Select(x => new
            {
                x.KpiResultId,
                Period = periodExpression.Invoke(x)
            })
            .ToList()
            .GroupBy(x => x.KpiResultId)
            .ToList();

        results.ForEach(result =>
        {
            // Assign each local kpi to its results.
            result.Periods = groupedResults
                .Where(x => x.Key == result.Id)
                .SelectMany(x => x.Select(y => y.Period))
                .ToList();
        });

        return results;
    }

    private static Expression<Func<KpiResult, KpiResultDetailDto>> Mapper(string lang, bool canAchievedByNegative)
    {
        var departmentExpression = DepartmentSimpleDto.Mapper(lang);
        var kpiExpression = KpiWithEvaluationDataDto.Mapper(lang);
        var isResultOwningDepartmentExpression = HelperExpression.IsKpiResultOwningDepartment();
        var kpiResultTargetSettingMethodExpression = KpiResultTargetSettingMethodGetDto.Mapper(lang);

        var aAggExpression = HelperExpression.AggregateAbValues("a");
        var bAggExpression = HelperExpression.AggregateAbValues("b");

        return item => new KpiResultDetailDto
        {
            Id = item.Id,
            Kpi = kpiExpression.Invoke(item.Kpi),
            Year = item.Year,
            Department = departmentExpression.Invoke(item.Department),
            Units = item.Units,
            UnitsDescription = item.UnitsDescription,
            Formula = item.Formula,
            FormulaDescriptionA = lang.Equals(SupportedCultures.LanguageArabic)
                ? item.FormulaDescriptionAAr
                : item.FormulaDescriptionAEn,
            FormulaDescriptionB = lang.Equals(SupportedCultures.LanguageArabic)
                ? item.FormulaDescriptionBAr
                : item.FormulaDescriptionBEn,
            Target = item.Kpi.IsTrend == 1 ? null : item.Target,
            TargetZero = item.Kpi.IsTrend == 1 ? null : item.TargetZero,
            AggregateA = aAggExpression.Invoke(item),
            AggregateB = bAggExpression.Invoke(item),
            Result = ObtainKpiResultSqlFunction.Call(item.KpiId, item.Year, item.DepartmentId),
            CurrentPeriod = ObtainKpiResultCurrentPeriodSqlFunction.Call(item.Year, item.MeasurementCycle),
            Achieved = item.Kpi.IsTrend == 1
                ? null
                : ObtainKpiAchievedSqlFunction.Call(item.KpiId, item.Year, item.DepartmentId,
                    canAchievedByNegative),
            MeasurementCycle = item.MeasurementCycle,
            MeasurementMethod = item.MeasurementMethod,
            CapabilityCount = item.Periods.SelectMany(x => x.Capabilities).Count(),
            LibraryFileCount = item.Attachments.Count(),
            TargetSettingMethod = item.TargetSettingMethod != null
                ? kpiResultTargetSettingMethodExpression.Invoke(item.TargetSettingMethod)
                : null,
            IsOwningDepartment = isResultOwningDepartmentExpression.Invoke(item),
            AggregationTypeA = item.AggregationTypeA,
            AggregationTypeB = item.AggregationTypeB,
            DecimalPlaces = item.DecimalPlaces > 2 ? item.DecimalPlaces : item.Kpi.DecimalPlaces
        };
    }

    public static Expression<Func<KpiResultPeriod, KpiResultPeriodWithEvaluationDto>> PeriodMapper(
        bool canAchievedBeNegative)
    {
        return item => new KpiResultPeriodWithEvaluationDto
        {
            Id = item.Id,
            Period = item.Period,
            CapabilityCount = item.Capabilities.Count,
            AttachmentCount = item.KpiResult.Attachments.Count(x => x.Period == item.Period),
            A = ObtainKpiResultPeriodParamSqlFunction.Call(item.Id, "a"),
            B = ObtainKpiResultPeriodParamSqlFunction.Call(item.Id, "b"),
            Target = item.KpiResult.Kpi.IsTrend == 1 ? null : item.Target,
            Result = EvaluateKpiFormulaSqlFunction.Call(
                item.KpiResult.Formula,
                ObtainKpiResultPeriodParamSqlFunction.Call(item.Id, "a"),
                ObtainKpiResultPeriodParamSqlFunction.Call(item.Id, "b"), 0
            ),
            Achieved = item.KpiResult.Kpi.IsTrend == 1
                ? null
                : ObtainAchievedFromResultAndTargetSqlFunction.Call(
                    EvaluateKpiFormulaSqlFunction.Call(
                        item.KpiResult.Formula,
                        ObtainKpiResultPeriodParamSqlFunction.Call(item.Id, "a"),
                        ObtainKpiResultPeriodParamSqlFunction.Call(item.Id, "b"), 0
                    ),
                    item.Target,
                    item.KpiResult.TargetZero,
                    item.KpiResult.Kpi.Direction,
                    canAchievedBeNegative
                ),
            IsApproved = item.IsApproved == 1,
            SupervisorNote = item.SupervisorNote,
            LeadershipDirective = item.LeadershipDirective,
            ResultAnalysis = item.ResultAnalysis,
            ImprovementProcedure = item.ImprovementProcedure,
            ImprovementProcedureCompletionPercentage = item.ImprovementProcedureCompletionPercentage,
            ImprovementProcedureExpectedCompletionDate = item.ImprovementProcedureExpectedCompletionDate,
            DecimalPlaces = item.KpiResult.DecimalPlaces > 2
                ? item.KpiResult.DecimalPlaces
                : item.KpiResult.Kpi.DecimalPlaces
        };
    }
}