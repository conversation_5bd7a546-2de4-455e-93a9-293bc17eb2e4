<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="MNMWebApp" Version="4.0.7" />
        <PackageReference Include="Anthropic.SDK" Version="5.3.0" />
        <PackageReference Include="EFCore.NamingConventions" Version="9.0.0"/>
        <PackageReference Include="EPPlus" Version="7.4.1"/>
        <PackageReference Include="ErrorOr" Version="2.0.1"/>
        <PackageReference Include="Humanizer" Version="2.14.1"/>
        <PackageReference Include="LinqKit" Version="1.1.24" />
        <PackageReference Include="Mapster" Version="7.4.0"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.3"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.3">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.3"/>
        <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.4" />
        <PackageReference Include="Minio" Version="6.0.2"/>
        <PackageReference Include="ModelContextProtocol" Version="0.2.0-preview.1" />
        <PackageReference Include="OpenAI" Version="2.1.0" />
        <PackageReference Include="QuestPDF" Version="2024.6.4"/>
        <PackageReference Include="SixLabors.ImageSharp" Version="3.1.7"/>
        <PackageReference Include="Stateless" Version="5.16.0"/>
        <PackageReference Include="MediatR" Version="11.1.0" />
    </ItemGroup>

    <ItemGroup>
        <EmbeddedResource Include="Fonts\**\*.ttf"/>
    </ItemGroup>

    <ItemGroup>
        <Reference Include="Masar">
          <HintPath>..\..\masar\backend\App\Masar\bin\Release\net6.0\Masar.dll</HintPath>
        </Reference>
        <Reference Include="Masar.Core">
          <HintPath>..\..\masar\backend\App\Masar.Core\bin\Debug\net6.0\Masar.Core.dll</HintPath>
        </Reference>
        <Reference Include="Masar.ModuleKpi">
          <HintPath>..\..\masar\backend\Modules\Kpi\Masar.ModuleKpi\bin\Release\net6.0\Masar.ModuleKpi.dll</HintPath>
        </Reference>
        <Reference Include="Masar.ModuleKpiResult">
          <HintPath>..\..\masar\backend\Modules\KpiResult\Masar.ModuleKpiResult\bin\Release\net6.0\Masar.ModuleKpiResult.dll</HintPath>
        </Reference>
        <Reference Include="UnifiedHub.Common">
            <HintPath>..\..\unifiedhub\backend\UnifiedHub.Common\bin\Debug\net9.0\UnifiedHub.Common.dll</HintPath>
        </Reference>
        <Reference Include="UnifiedHub.Core">
            <HintPath>..\..\unifiedhub\backend\UnifiedHub.Core\bin\Debug\net9.0\UnifiedHub.Core.dll</HintPath>
        </Reference>
        <Reference Include="UnifiedHub.Flows">
          <HintPath>..\..\unifiedhub\backend\Features\UnifiedHub.Flows\bin\Debug\net9.0\UnifiedHub.Flows.dll</HintPath>
        </Reference>
        <Reference Include="UnifiedHub.Model">
            <HintPath>..\..\unifiedhub\backend\UnifiedHub.Model\bin\Debug\net9.0\UnifiedHub.Model.dll</HintPath>
        </Reference>
        <Reference Include="UnifiedHub.Persistence">
            <HintPath>..\..\unifiedhub\backend\UnifiedHub.Persistence\bin\Debug\net9.0\UnifiedHub.Persistence.dll</HintPath>
        </Reference>
    </ItemGroup>

    <!--    <ItemGroup>-->
    <!--      <Reference Include="UnifiedHub.AttendanceManagement.Core">-->
    <!--        <HintPath>..\..\unifiedhub\backend\Modules\AttendanceManagement\UnifiedHub.AttendanceManagement\bin\Debug\net8.0\UnifiedHub.AttendanceManagement.dll</HintPath>-->
    <!--      </Reference>-->
    <!--      <Reference Include="UnifiedHub.Auth">-->
    <!--        <HintPath>..\..\unifiedhub\backend\Features\UnifiedHub.Auth\bin\Debug\net8.0\UnifiedHub.Auth.dll</HintPath>-->
    <!--      </Reference>-->
    <!--      <Reference Include="UnifiedHub.Common">-->
    <!--        <HintPath>..\..\unifiedhub\backend\UnifiedHub.Persistence\bin\Debug\net8.0\UnifiedHub.Common.dll</HintPath>-->
    <!--      </Reference>-->
    <!--      <Reference Include="UnifiedHub.Core">-->
    <!--        <HintPath>..\..\unifiedhub\backend\UnifiedHub.Persistence\bin\Debug\net8.0\UnifiedHub.Core.dll</HintPath>-->
    <!--      </Reference>-->
    <!--      <Reference Include="UnifiedHub.Dtos">-->
    <!--        <HintPath>..\..\unifiedhub\backend\Features\UnifiedHub.Auth\bin\Debug\net8.0\UnifiedHub.Dtos.dll</HintPath>-->
    <!--      </Reference>-->
    <!--      <Reference Include="UnifiedHub.Model">-->
    <!--        <HintPath>..\..\unifiedhub\backend\UnifiedHub.Persistence\bin\Debug\net8.0\UnifiedHub.Model.dll</HintPath>-->
    <!--      </Reference>-->
    <!--      <Reference Include="UnifiedHub.Persistence">-->
    <!--        <HintPath>..\..\unifiedhub\backend\UnifiedHub.Persistence\bin\Debug\net8.0\UnifiedHub.Persistence.dll</HintPath>-->
    <!--      </Reference>-->
    <!--    </ItemGroup>-->
</Project>
