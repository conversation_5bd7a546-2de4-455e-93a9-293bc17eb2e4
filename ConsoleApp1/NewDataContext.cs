using Masar.Core.Models;
using Masar.Core.Models.Abstraction;
using Masar.Core.Models.DomainClasses.App;
using Masar.Core.Models.DomainClasses.App.EvaluationModel;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

namespace ConsoleApp1;

public class NewDataContext : DbContext
{
    public NewDataContext(DbContextOptions<NewDataContext> options) : base(options)
    {
    }

    public DbSet<Kpi> Kpis { get; set; }
    public DbSet<Department> Departments { get; set; }
    public DbSet<EvaluationScoreBand> EvaluationScoreBands { get; set; }
    public DbSet<Tournament> Tournaments { get; set; }
    public DbSet<Standard> Standards { get; set; }
    public DbSet<StrategicGoal> StrategicGoals { get; set; }
    public DbSet<Plan> Plans { get; set; }
    public DbSet<PlanTask> PlanTasks { get; set; }
    public DbSet<PlanSubtask> PlanSubtasks { get; set; }
    public DbSet<ImprovementOpportunity> ImprovementOpportunities { get; set; }

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);

        FluentApis.Setup(builder);

        var dateTimeConverter = new ValueConverter<DateTime, DateTime>(
            v => v, v => DateTime.SpecifyKind(v, DateTimeKind.Utc));

        foreach (var entityType in builder.Model.GetEntityTypes())
        {
            foreach (var property in entityType.GetProperties())
            {
                if (property.ClrType == typeof(DateTime) || property.ClrType == typeof(DateTime?))
                    property.SetValueConverter(dateTimeConverter);
            }
        }
    }

    protected override void ConfigureConventions(ModelConfigurationBuilder configurationBuilder)
    {
        configurationBuilder.Properties<string[]>()
            .HaveConversion<AppBaseDbContext.JsonArrayConverter<string>>();
    }
}