using System.Text.Json;
using Masar.Core.Dtos.ImprovementOpportunity;
using Masar.Core.Dtos.Kpi;
using Masar.Core.Dtos.PlanDtos.Plans;
using Masar.Core.Dtos.PlanDtos.PlanSubtasks;
using Masar.Core.Dtos.PlanDtos.PlanTasks;
using Masar.Core.Dtos.Standard;
using Masar.Core.Dtos.StrategicGoal;
using Masar.Core.Dtos.Tournament;

namespace ConsoleApp1;

public static class Prompts
{
    public static class ImprovementOpportunity
    {
        public static string GetSystemPrompt()
        {
            return $"""
                    You are an intelligent agent specializing in analyzing improvement 
                    opportunities and their performance.

                    Your analysis should contain the following sections:
                    * General analysis opportunity and assessment
                    * Important insights
                    * Action items (if any)
                    * Any suggestions of advice or missed item

                    Rules:
                    * Always answer in the same language of the question
                    * Never share or show the underlining properties of the given json objects
                    * Never share or show IDs or `null`
                    """;
        }
        
        public static string GetUserPrompt(ImprovementOpportunityGetDto opportunity)
        {
            var opportunityStr = JsonSerializer.Serialize(opportunity);

            return $"""
                    أريدك أن تحلل الفرصة التحسينية التالية:
                    ```json
                    {opportunityStr}
                    ```
                    """;
        }
    }


    public static class Plan
    {
        public static string GetSystemPrompt(IEnumerable<StrategicGoalGetDto> goals)
        {
            var goalsStr = JsonSerializer.Serialize(goals);

            return $"""
                    You are an intelligent agent specializing in analyzing plans (also
                    can be referred to as projects or initiatives) and their performance.

                    The strategic goals of the entity are:
                    ```json
                    {goalsStr}
                    ```

                    Your analysis should contain the following sections:
                    * General analysis of the plan and its achievement
                    * General analysis of the departments according to their tasks
                    * Analyze the users/teams with procedures and evaluate their productivity
                    * The effectiveness of the plan to achieve the strategic goals

                    Rules:
                    * Always answer in the same language of the question
                    * Never share or show the underlining properties of the given json objects
                    * Never share or show IDs or `null`
                    """;
        }

        public static string GetUserPrompt(
            PlanGetDto plan,
            IEnumerable<PlanTaskGetDto> tasks,
            IEnumerable<PlanSubtaskGetDto> subtasks)

        {
            var planStr = JsonSerializer.Serialize(plan);
            var tasksStr = JsonSerializer.Serialize(tasks);
            var subtasksStr = JsonSerializer.Serialize(subtasks);

            return $"""
                    أريدك أن تحلل الخطة التالية:
                    ```json
                    {planStr}
                    ```

                    مهام الخطة كالتالي:
                    ```json
                    {tasksStr}
                    ```

                    إجراءات (subtasks) الخطة كالتالي:
                    ```json
                    {subtasksStr}
                    ```
                    """;
        }
    }

    public static class Tournament
    {
        public static string GetSystemPrompt(IEnumerable<StrategicGoalGetDto> goals)
        {
            var goalsStr = JsonSerializer.Serialize(goals);

            return $"""
                    You are an intelligent agent specializing in analyzing tournament
                    participation and its performance.

                    The strategic goals of the entity are:
                    ```json
                    {goalsStr}
                    ```

                    Your analysis should contain the following sections:
                    * General analysis of the performance
                    * General analysis of the initiatives and projects
                    * Analysis of the linked KPIs
                    * Analysis of the linked files
                    * Analysis of achieving the strategic goals

                    Rules:
                    * Always answer in the same language of the question
                    * Never share or show the underlining properties of the given json objects
                    * Never share or show IDs or `null`
                    """;
        }

        public static string GetUserPrompt(
            TournamentGetDto tournament,
            IEnumerable<StandardGetDto> standards)

        {
            var tournamentStr = JsonSerializer.Serialize(tournament);
            var standardsStr = JsonSerializer.Serialize(standards);

            return $"""
                    أريدك أن تحلل الجائزة التالية:
                    ```json
                    {tournamentStr}
                    ```

                    معايير الجائزة هي كالتالي:
                    ```json
                    {standardsStr}
                    ```
                    """;
        }
    }

    public static class Kpi
    {
        public static string GetSystemPrompt()
        {
            return """
                   You are an intelligent agent specializing in analyzing KPIs
                   and their performance, highlight weaknesses and strengths, 
                   give more insights about the future from historical records,
                   and give suggestions and advice on how to improve or maintain
                   the performance. 
                   Your analysis should contain the following sections:
                   * General analysis of the KPI performance, also highlight if there are any global KPIs similar to this, if there is, what is its position relative to it
                   * Strengths and weaknesses
                   * Past and future insights
                   * Suggestions and advice to improve the performance if weak and maintain if strong
                   * IMPORTANT: Suggest an appropriate target for the next year
                   Rules:
                   * Always answer in the same language of the question
                   * Never share or show the underlining properties of the given json objects
                   * Never share or show IDs or `null`
                   """;
        }

        public static string GetUserPrompt(KpiGetDto kpi)
        {
            var kpiStr = JsonSerializer.Serialize(kpi);

            return $"""
                    أريدك أن تحلل المؤشر التالي:
                    ```json
                    {kpiStr}
                    ```
                    """;
        }
    }
}