using System.Linq.Expressions;
using LinqKit;
using Masar.Core;
using Masar.Core.Abstraction;
using Masar.Core.Constants;
using Masar.Core.DtoMappingStrategies;
using Masar.Core.Dtos.Department;
using Masar.Core.Dtos.Kpi;
using Masar.Core.Dtos.KpiType;
using Masar.Core.Dtos.Operation;
using Masar.Core.Dtos.OperationProcedure;
using Masar.Core.Dtos.StrategicGoal;
using Masar.Core.Extensions;
using Masar.Core.Models.DomainClasses.App;

namespace ConsoleApp1;

public class NewKpiGetDtoMappingStrategy : IDtoMappingStrategy<Kpi, KpiGetDto>
{
    private readonly int _fromYear;
    private readonly int _toYear;
    private readonly bool _canAchievedBeNegative;
    private readonly bool _includeAllResults;

    public NewKpiGetDtoMappingStrategy(
        bool canAchievedBeNegative,
        int fromYear = -1,
        int toYear = -1,
        bool includeAllResults = false
    )
    {
        var currentYear = DateTime.UtcNow.Year;

        _fromYear = fromYear == -1 ? currentYear - 4 : fromYear;
        _toYear = toYear == -1 ? fromYear + 5 : toYear;
        _canAchievedBeNegative = canAchievedBeNegative;
        _includeAllResults = includeAllResults;
    }

    public IEnumerable<KpiGetDto> Execute(IQueryable<Kpi> source)
    {
        // Map all kpis.
        var kpis = source
            .Select(Mapper(HelperFunctions.GetLanguageCode(), _fromYear, _toYear))
            .ToList();

        var kpiIds = kpis.Select(x => x.Id);
        var groupedResults = source
            .Where(x => kpiIds.Contains(x.Id))
            .SelectMany(x => x.Results)
            .Where(y => _includeAllResults || (_fromYear <= y.Year && y.Year < _toYear))
            .OrderByDescending(y => y.Year)
            .Map(new NewKpiResultDetailDtoMappingStrategy(
                _canAchievedBeNegative
            ))
            .GroupBy(x => x.Kpi.Id);

        kpis.ForEach(kpi => kpi.Results = groupedResults
            .Where(x => x.Key == kpi.Id)
            .SelectMany(x => x)
        );

        return kpis;
    }

    private static Expression<Func<Kpi, KpiGetDto>> Mapper(
        string lang,
        int fromYear,
        int toYear)
    {
        var typeExpression = KpiTypeWithCodeDto.Mapper(lang);
        var departmentSimpleExpression = DepartmentSimpleDto.Mapper(lang);
        var operationExpression = OperationWithOwnerAndLevelDto.Mapper(lang);
        var operationProcedureExpression = OperationProcedureWithCodeDto.Mapper(lang);
        var strategicGoalsExpression = StrategicGoalSimpleDto.Mapper(lang);

        return item => new KpiGetDto
        {
            Id = item.Id,
            Code = item.Code,
            Type = typeExpression.Invoke(item.Type),
            Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
            Units = item.Units,
            UnitsDescription = item.UnitsDescription,
            Formula = item.Formula,
            FormulaDescriptionA = lang == SupportedCultures.LanguageArabic
                ? item.FormulaDescriptionAAr
                : item.FormulaDescriptionAEn,
            FormulaDescriptionB = lang == SupportedCultures.LanguageArabic
                ? item.FormulaDescriptionBAr
                : item.FormulaDescriptionBEn,
            Direction = item.Direction,
            Source = item.Source,
            CreationYear = item.CreationYear,
            Description = lang == SupportedCultures.LanguageArabic ? item.DescriptionAr : item.DescriptionEn,
            MeasurementCycle = item.MeasurementCycle,
            DataEntryMethod = item.DataEntryMethod,
            OwningDepartment = departmentSimpleExpression.Invoke(item.OwningDepartment),
            MeasuringDepartment = item.MeasuringDepartment == null
                ? null
                : departmentSimpleExpression.Invoke(item.MeasuringDepartment),
            ResultsFromYear = fromYear,
            ResultsToYear = toYear,
            IsSpecial = item.IsSpecial == 1,
            IsTrend = item.IsTrend == 1,
            InitialResultSource = item.InitialResultSource,
            InitialResult = item.InitialResult,
            InitialResultDetails = item.InitialResultDetails,

            Operations = item.OperationLinks
                .Select(x => operationExpression.Invoke(x.Operation))
                .ToList(),

            OperationProcedures = item.OperationProcedureLinks
                .Select(x => operationProcedureExpression.Invoke(x.Procedure))
                .ToList(),

            StrategicGoals = item.StrategicGoalLinks.Select(x => strategicGoalsExpression.Invoke(x.StrategicGoal))
                .ToList(),
            DecimalPlaces = item.DecimalPlaces
        };
    }
}